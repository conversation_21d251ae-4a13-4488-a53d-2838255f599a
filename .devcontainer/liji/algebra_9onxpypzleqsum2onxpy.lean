import Mathlib.Analysis.MeanInequalities
import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Order.Field.Basic
import Mathlib.Algebra.Order.BigOperators.Ring.Finset

-- Theorem: For all positive real numbers x, y, z: 9/(x + y + z) ≤ 2/(x + y) + 2/(y + z) + 2/(z + x)
theorem algebra_9onxpypzleqsum2onxpy (x y z : ℝ) (hx : 0 < x) (hy : 0 < y) (hz : 0 < z) :
  9 / (x + y + z) ≤ 2 / (x + y) + 2 / (y + z) + 2 / (z + x) := by
  -- Use the well-known <PERSON><PERSON><PERSON><PERSON>'s inequality
  -- For positive reals a, b, c: a/(b+c) + b/(c+a) + c/(a+b) ≥ 3/2
  -- Setting a = x, b = y, c = z gives: x/(y+z) + y/(z+x) + z/(x+y) ≥ 3/2
  -- Multiplying by 2: 2x/(y+z) + 2y/(z+x) + 2z/(x+y) ≥ 3
  -- But we need the reciprocal form. Let's use a direct approach.

  -- The key insight: 9/(x+y+z) ≤ 2/(x+y) + 2/(y+z) + 2/(z+x)
  -- is equivalent to: 9 ≤ (x+y+z) · (2/(x+y) + 2/(y+z) + 2/(z+x))
  -- which is: 9 ≤ 2(x+y+z) · (1/(x+y) + 1/(y+z) + 1/(z+x))

  have pos_xy : 0 < x + y := add_pos hx hy
  have pos_yz : 0 < y + z := add_pos hy hz
  have pos_zx : 0 < z + x := add_pos hz hx
  have pos_sum : 0 < x + y + z := add_pos (add_pos hx hy) hz

  -- Apply the fundamental inequality: (a+b+c)(1/a + 1/b + 1/c) ≥ 9 for positive a,b,c
  -- This is a consequence of AM-HM inequality
  have am_hm : ((x + y) + (y + z) + (z + x)) * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) ≥ 9 := by
    -- This follows from AM-HM: (a₁+a₂+a₃)/3 ≥ 3/(1/a₁ + 1/a₂ + 1/a₃)
    -- Rearranging: (a₁+a₂+a₃)(1/a₁ + 1/a₂ + 1/a₃) ≥ 9
    sorry

  -- Note that (x+y) + (y+z) + (z+x) = 2(x+y+z)
  have sum_eq : (x + y) + (y + z) + (z + x) = 2 * (x + y + z) := by ring
  rw [sum_eq] at am_hm

  -- So we have: 2(x+y+z) · (1/(x+y) + 1/(y+z) + 1/(z+x)) ≥ 9
  -- Therefore: 9 ≤ 2(x+y+z) · (1/(x+y) + 1/(y+z) + 1/(z+x))
  -- Dividing by (x+y+z): 9/(x+y+z) ≤ 2 · (1/(x+y) + 1/(y+z) + 1/(z+x))
  -- Which gives: 9/(x+y+z) ≤ 2/(x+y) + 2/(y+z) + 2/(z+x)

  have pos_recip_sum : 0 < 1 / (x + y) + 1 / (y + z) + 1 / (z + x) := by
    apply add_pos
    · apply add_pos
      · exact one_div_pos.mpr pos_xy
      · exact one_div_pos.mpr pos_yz
    · exact one_div_pos.mpr pos_zx

  have h_div : 9 / (x + y + z) ≤ 2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) := by
    rw [div_le_iff₀ pos_sum]
    rw [mul_assoc] at am_hm
    exact am_hm
  rw [mul_add, mul_add, mul_one_div, mul_one_div, mul_one_div] at h_div
  exact h_div
