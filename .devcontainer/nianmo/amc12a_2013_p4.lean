-- AMC 12A 2013 Problem 4: Evaluate (2^{2014}+2^{2012}) / (2^{2014}-2^{2012})
-- Solution approach: Factor out 2^{2012} and simplify

import Mathlib.Algebra.Group.Defs
import Mathlib.Tactic.Ring

theorem amc12a_2013_p4 : (2^2014 + 2^2012) / (2^2014 - 2^2012) = 5 / 3 := by
  -- Direct computation approach
  -- Step 1: Rewrite using the fact that 2^2014 = 2^2012 * 2^2 = 2^2012 * 4
  have h1 : 2^2014 = 2^2012 * 4 := by
    rw [show 2^2014 = 2^(2012 + 2) from rfl, pow_add]
    norm_num

  -- Step 2: Substitute and simplify
  rw [h1]
  -- Now we have: (2^2012 * 4 + 2^2012) / (2^2012 * 4 - 2^2012) = 5 / 3
  ring_nf
  -- This simplifies to: 5 / 3
