import Mathlib.Data.Finset.Card
import Mathlib.Data.Finset.Basic
import Mathlib.Data.Nat.Basic
import Mathlib.Data.Nat.Digits
import Mathlib.Tactic

-- AMC 12A 2020 Problem 4: Count 4-digit integers with all even digits divisible by 5

def is_even_digit (d : ℕ) : Prop := d ∈ ({0, 2, 4, 6, 8} : Finset ℕ)

instance : DecidablePred is_even_digit := fun d =>
  Finset.decidableMem d {0, 2, 4, 6, 8}

def is_four_digit (n : ℕ) : Prop := 1000 ≤ n ∧ n ≤ 9999

instance : DecidablePred is_four_digit := fun n =>
  instDecidableAnd

def all_digits_even (n : ℕ) : Prop :=
  ∀ d ∈ (Nat.digits 10 n), is_even_digit d

instance : DecidablePred all_digits_even := fun n =>
  decidable_of_iff' _ List.forall_iff_forall_mem.symm

def valid_number (n : ℕ) : Prop :=
  is_four_digit n ∧ all_digits_even n ∧ n % 5 = 0

instance : DecidablePred valid_number := fun n =>
  instDecidableAnd

-- Main theorem
theorem amc12a_2020_p4 :
  (Finset.filter valid_number (Finset.range 10000)).card = 100 := by
  sorry

-- Helper lemmas for each subgoal

lemma units_digit_constraint (n : ℕ) (h1 : is_four_digit n) (h2 : all_digits_even n) (h3 : n % 5 = 0) :
  n % 10 = 0 := by
  -- n % 5 = 0 means n % 10 ∈ {0, 5}
  have h_mod_10 : n % 10 = 0 ∨ n % 10 = 5 := by
    have h_mod : n % 10 % 5 = 0 := by
      have : n % 10 % 5 = n % 5 := Nat.mod_mod_of_dvd n (by norm_num : 5 ∣ 10)
      rw [this, h3]
    -- Check all possible values of n % 10
    have h_lt : n % 10 < 10 := Nat.mod_lt n (by norm_num)
    interval_cases (n % 10)
    · left; rfl
    · simp at h_mod
    · simp at h_mod
    · simp at h_mod
    · simp at h_mod
    · right; rfl
    · simp at h_mod
    · simp at h_mod
    · simp at h_mod
    · simp at h_mod
  -- But n % 10 must be even (from all_digits_even)
  have h_even : is_even_digit (n % 10) := by
    have h_pos : 0 < n := by
      have : 1000 ≤ n := h1.1
      omega
    have h_in_digits : n % 10 ∈ Nat.digits 10 n := by
      rw [Nat.digits_def' (by norm_num) h_pos]
      simp [Nat.mod_lt]
    exact h2 (n % 10) h_in_digits
  -- 5 is not even, so n % 10 ≠ 5
  cases h_mod_10 with
  | inl h => exact h
  | inr h =>
    rw [h] at h_even
    simp [is_even_digit] at h_even

lemma thousands_digit_choices (d₁ : ℕ) (h : d₁ ∈ ({2, 4, 6, 8} : Finset ℕ)) :
  is_even_digit d₁ ∧ d₁ ≠ 0 := by
  sorry

lemma hundreds_tens_digit_choices (d : ℕ) (h : d ∈ ({0, 2, 4, 6, 8} : Finset ℕ)) :
  is_even_digit d := by
  sorry

lemma multiplication_principle :
  4 * 5 * 5 * 1 = 100 := by
  sorry
